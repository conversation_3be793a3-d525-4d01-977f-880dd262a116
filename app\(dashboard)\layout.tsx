import type React from "react"
import Link from "next/link"

async function getMe() {
  const res = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || ""}/api/me`, {
    // RSC fetch; cookie is forwarded automatically by Next.js
    cache: "no-store",
  })
  if (!res.ok) return null
  return res.json() as Promise<{
    user: { id: string; full_name: string; email: string; roles: string[] }
    org: { id: string; name: string }
    modules: { key: string; label: string; path: string }[]
  }>
}

export default async function DashboardLayout({ children }: { children: React.ReactNode }) {
  const me = await getMe()

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b border-border bg-card text-card-foreground">
        <div className="mx-auto max-w-7xl px-6 py-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="h-6 w-6 rounded bg-primary" aria-hidden />
            <Link href="/dashboard" className="font-semibold">
              FacilityOS
            </Link>
          </div>
          <div className="text-sm text-foreground/80">
            {me ? (
              <span>
                Org: <strong>{me.org.name}</strong> — {me.user.full_name} ({me.user.roles.join(", ")})
              </span>
            ) : (
              <span>Not signed in</span>
            )}
          </div>
        </div>
      </header>
      <div className="mx-auto max-w-7xl px-6 py-6 grid gap-6 md:grid-cols-[240px_1fr]">
        <aside className="rounded-lg border border-border bg-card text-card-foreground">
          <nav className="p-3">
            <div className="mb-2 px-2 text-xs font-medium text-foreground/60">Modules</div>
            <ul className="space-y-1">
              {me?.modules?.map((m) => (
                <li key={m.key}>
                  <Link href={m.path} className="block rounded px-2 py-1.5 text-sm hover:bg-muted">
                    {m.label}
                  </Link>
                </li>
              )) || <li className="px-2 py-1.5 text-sm text-foreground/60">No modules</li>}
              <li className="mt-3">
                <form action="/api/auth/logout" method="post">
                  <button className="w-full rounded px-2 py-1.5 text-left text-sm hover:bg-muted">Logout</button>
                </form>
              </li>
            </ul>
          </nav>
        </aside>
        <main>{children}</main>
      </div>
    </div>
  )
}
