async function getSpaces() {
  const res = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || ""}/api/properties?limit=20`, { cache: "no-store" })
  if (!res.ok) return []
  return res.json() as Promise<Array<{ id: string; code: string; kind: string; status: string; area_sqft?: number }>>
}
export default async function PropertyModulePage() {
  const spaces = await getSpaces()
  return (
    <div>
      <h1 className="text-2xl font-semibold">Property / Spaces</h1>
      <div className="mt-4 rounded-lg border border-border bg-card text-card-foreground">
        <table className="w-full text-sm">
          <thead className="bg-muted text-left text-foreground/70">
            <tr>
              <th className="px-3 py-2">Code</th>
              <th className="px-3 py-2">Kind</th>
              <th className="px-3 py-2">Status</th>
              <th className="px-3 py-2">Area (sqft)</th>
            </tr>
          </thead>
          <tbody>
            {spaces.map((s) => (
              <tr key={s.id} className="border-t border-border">
                <td className="px-3 py-2">{s.code}</td>
                <td className="px-3 py-2">{s.kind}</td>
                <td className="px-3 py-2">{s.status}</td>
                <td className="px-3 py-2">{s.area_sqft ?? "-"}</td>
              </tr>
            ))}
            {spaces.length === 0 && (
              <tr>
                <td className="px-3 py-6 text-foreground/60" colSpan={4}>
                  No spaces found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}
