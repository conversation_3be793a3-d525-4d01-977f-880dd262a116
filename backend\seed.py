from .database import Base, engine, SessionLocal
from .models import Org, Site, User, Role, UserRole, Space, Lease, ServiceRequest
from .auth import hash_password
from sqlalchemy.orm import Session
from datetime import date, timedelta

def seed():
    Base.metadata.create_all(bind=engine)
    db: Session = SessionLocal()
    try:
        # Org/Site
        org = Org(name="Acme Estates")
        db.add(org); db.flush()
        site = Site(org_id=org.id, name="Acme Tech Park", code="ATP-BLR", kind="commercial")
        db.add(site); db.flush()
        # Roles
        roles = {}
        for r in ["super_admin","manager","tenant","staff","finance"]:
            role = Role(org_id=org.id, name=r, description=f"{r} role")
            db.add(role); db.flush()
            roles[r]=role
        # Users
        def add_user(name,email,roles_list):
            u = User(org_id=org.id, full_name=name, email=email, password_hash=hash_password("password123"))
            db.add(u); db.flush()
            for rn in roles_list:
                db.add(UserRole(user_id=u.id, role_id=roles[rn].id))
            return u
        sa = add_user("Super Admin","<EMAIL>",["super_admin"])
        mgr = add_user("Property Manager","<EMAIL>",["manager"])
        ten = add_user("John Tenant","<EMAIL>",["tenant"])
        fin = add_user("Pay Accountant","<EMAIL>",["finance"])
        stf = add_user("Field Staff","<EMAIL>",["staff"])
        # Spaces
        spaces=[]
        for i in range(1,11):
            sp = Space(org_id=org.id, site_id=site.id, code=f"A-{i:03}", kind="office", status="available" if i%3 else "occupied", area_sqft=1000+i*25)
            db.add(sp); db.flush()
            spaces.append(sp)
        # Leases
        for i, sp in enumerate(spaces[:4], start=1):
            lease = Lease(org_id=org.id, site_id=site.id, space_id=sp.id,
                          start_date=str(date.today().replace(day=1)),
                          end_date=str(date.today().replace(day=1) + timedelta(days=365)),
                          rent_amount=75000 + i*5000, status="active")
            db.add(lease)
        # Service Requests
        cats = ["plumbing","electrical","cleaning","security"]
        for i in range(1,8):
            sr = ServiceRequest(org_id=org.id, site_id=site.id, space_id=spaces[i].id,
                                category=cats[i%len(cats)], priority="high" if i%4==0 else "medium",
                                status="open" if i%3 else "in_progress", description=f"Issue #{i}")
            db.add(sr)
        db.commit()
        print("[seed] Database seeded with mock data.")
    finally:
        db.close()

if __name__ == "__main__":
    seed()
