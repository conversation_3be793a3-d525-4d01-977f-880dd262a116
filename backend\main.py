from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from .database import SessionLocal, engine, Base
from .models import User, Role, UserRole, Org, Site, Space, Lease, ServiceRequest
from .auth import verify_password, sign_jwt, decode_jwt, JWT_EXPIRES_IN
from .schemas import TokenResponse, MeResponse, MeOrg, MeUser, ModuleItem, SummaryResponse, SpaceOut, LeaseOut, ServiceRequestOut
from typing import List, Optional

Base.metadata.create_all(bind=engine)

app = FastAPI(title="FacilityOS API", version="0.1.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For demo; tighten in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_current_user(request: Request, db: Session = Depends(get_db)) -> dict:
    auth = request.headers.get("Authorization", "")
    if not auth.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Unauthorized")
    token = auth.split(" ", 1)[1]
    try:
        payload = decode_jwt(token)
    except Exception:
        raise HTTPException(status_code=401, detail="Invalid token")
    user = db.query(User).filter(User.id == payload["sub"]).first()
    if not user or user.status != "active":
        raise HTTPException(status_code=401, detail="Unauthorized")
    roles: List[str] = payload.get("roles", [])
    return {"user": user, "org_id": payload["org_id"], "roles": roles}

MODULES = [
    ModuleItem(key="property", label="Property / Spaces", path="/modules/property"),
    ModuleItem(key="lease", label="Lease & Agreements", path="/modules/lease"),
    ModuleItem(key="maintenance", label="Maintenance", path="/modules/maintenance"),
    # Extend with finance, legal, crm, staff, etc.
]
MODULE_ACCESS = {
    "super_admin": ["property","lease","maintenance"],
    "manager": ["property","lease","maintenance"],
    "finance": ["lease"],
    "tenant": ["maintenance"],
    "staff": ["maintenance"],
}

@app.post("/auth/login", response_model=TokenResponse)
def login(payload: dict, db: Session = Depends(get_db)):
    email = (payload.get("email") or "").strip().lower()
    password = payload.get("password") or ""
    user = db.query(User).filter(User.email == email).first()
    if not user or not verify_password(password, user.password_hash):
        raise HTTPException(status_code=401, detail="Invalid credentials")
    # roles
    role_ids = [ur.role_id for ur in db.query(UserRole).filter(UserRole.user_id == user.id).all()]
    role_names = [r.name for r in db.query(Role).filter(Role.id.in_(role_ids)).all()]
    token = sign_jwt(sub=user.id, org_id=user.org_id, roles=role_names)
    return TokenResponse(access_token=token, expires_in=JWT_EXPIRES_IN)

@app.get("/me", response_model=MeResponse)
def me(ctx = Depends(get_current_user), db: Session = Depends(get_db)):
    user = ctx["user"]
    org = db.query(Org).filter(Org.id == ctx["org_id"]).first()
    roles = ctx["roles"]
    # Compute allowed modules
    allowed_keys = set()
    for r in roles:
        allowed_keys.update(MODULE_ACCESS.get(r, []))
    allowed = [m for m in MODULES if m.key in allowed_keys]
    return MeResponse(
        org=MeOrg(id=org.id, name=org.name),
        user=MeUser(id=user.id, full_name=user.full_name, email=user.email, roles=roles),
        modules=allowed,
    )

@app.get("/summary", response_model=SummaryResponse)
def summary(ctx = Depends(get_current_user), db: Session = Depends(get_db)):
    org_id = ctx["org_id"]
    total_spaces = db.query(Space).filter(Space.org_id == org_id).count()
    occupied = db.query(Space).filter(Space.org_id == org_id, Space.status == "occupied").count()
    occupancy = round((occupied / total_spaces) * 100, 2) if total_spaces else 0.0
    open_srs = db.query(ServiceRequest).filter(ServiceRequest.org_id == org_id, ServiceRequest.status.in_(["open","in_progress"])).count()
    total_rent = sum([float(l.rent_amount or 0) for l in db.query(Lease).filter(Lease.org_id == org_id).all()])
    return SummaryResponse(occupancy=occupancy, open_work_orders=open_srs, rent_due=total_rent, compliance_alerts=0)

@app.get("/spaces", response_model=List[SpaceOut])
def list_spaces(limit: int = 50, ctx = Depends(get_current_user), db: Session = Depends(get_db)):
    org_id = ctx["org_id"]
    rows = db.query(Space).filter(Space.org_id == org_id).limit(limit).all()
    return [SpaceOut(id=r.id, code=r.code, kind=r.kind, status=r.status, area_sqft=float(r.area_sqft or 0)) for r in rows]

@app.get("/leases", response_model=List[LeaseOut])
def list_leases(limit: int = 50, ctx = Depends(get_current_user), db: Session = Depends(get_db)):
    org_id = ctx["org_id"]
    rows = db.query(Lease).filter(Lease.org_id == org_id).limit(limit).all()
    # map space codes
    space_map = {s.id: s.code for s in db.query(Space).filter(Space.org_id == org_id).all()}
    out = []
    for l in rows:
        out.append(LeaseOut(
            id=l.id, space_code=space_map.get(l.space_id, "-"),
            start_date=l.start_date, end_date=l.end_date,
            rent_amount=float(l.rent_amount or 0), status=l.status
        ))
    return out

@app.get("/service-requests", response_model=List[ServiceRequestOut])
def list_service_requests(limit: int = 50, ctx = Depends(get_current_user), db: Session = Depends(get_db)):
    org_id = ctx["org_id"]
    rows = db.query(ServiceRequest).filter(ServiceRequest.org_id == org_id).order_by(ServiceRequest.created_at.desc()).limit(limit).all()
    space_map = {s.id: s.code for s in db.query(Space).filter(Space.org_id == org_id).all()}
    return [ServiceRequestOut(
        id=r.id, category=r.category or "-", priority=r.priority, status=r.status,
        created_at=r.created_at.isoformat() if r.created_at else "-", space_code=space_map.get(r.space_id)
    ) for r in rows]
