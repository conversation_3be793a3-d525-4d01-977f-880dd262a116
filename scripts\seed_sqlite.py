"""
Seed the SQLite database with the project's official seed function.

- Delegates to backend.seed.seed() to ensure models and credentials stay consistent.
- Prints summary and login credentials on success.

Run from v0 Scripts panel: "Seed DB"
"""

import sys

def main() -> int:
    try:
        from backend.seed import seed  # type: ignore
    except Exception as e:
        print(f"[seed] Import error: {e}")
        print("[seed] Ensure backend/__init__.py exists and backend/seed.py is present.")
        return 1

    try:
        seed()
    except Exception as e:
        print(f"[seed] Error while seeding: {e}")
        return 1

    print("\n[seed] Done. Sample login credentials:")
    print("  - <EMAIL> / password123  (role: super_admin)")
    print("  - <EMAIL> / password123      (role: manager)")
    print("  - <EMAIL> / password123       (role: tenant)")
    print("  - <EMAIL> / password123      (role: finance)")
    print("  - <EMAIL> / password123        (role: staff)")
    return 0

if __name__ == "__main__":
    sys.exit(main())
