import { type NextRequest, NextResponse } from "next/server"
import { getFastApiBase } from "@/app/lib/env"

export async function GET(req: NextRequest) {
  const token = req.cookies.get("fm_jwt")?.value
  const base = getFastApiBase()

  console.log("Debug /api/me:", { base, hasToken: !!token })

  if (!base) return new NextResponse("FASTAPI_BASE_URL not set", { status: 500 })
  if (!token) return new NextResponse("Unauthorized", { status: 401 })

  try {
    const res = await fetch(`${base}/me`, {
      headers: { Authorization: `Bearer ${token}` },
      cache: "no-store",
    })

    console.log("Backend /me response:", { status: res.status, ok: res.ok })

    if (!res.ok) {
      const errorText = await res.text()
      console.log("Backend error:", errorText)
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const me = await res.json()
    return NextResponse.json(me)
  } catch (error) {
    console.error("Error fetching /me:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}
