import { type NextRequest, NextResponse } from "next/server"
import { getFastApiBase } from "@/app/lib/env"

export async function GET(req: NextRequest) {
  const token = req.cookies.get("fm_jwt")?.value
  const base = getFastApiBase()
  if (!base) return new NextResponse("FASTAPI_BASE_URL not set", { status: 500 })
  if (!token) return new NextResponse("Unauthorized", { status: 401 })

  const res = await fetch(`${base}/me`, {
    headers: { Authorization: `Bearer ${token}` },
    cache: "no-store",
  })
  if (!res.ok) return new NextResponse("Unauthorized", { status: 401 })
  const me = await res.json()
  return NextResponse.json(me)
}
