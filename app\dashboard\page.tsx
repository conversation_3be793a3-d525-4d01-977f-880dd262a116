import Link from "next/link"

type Summary = {
  properties: number
  spaces: number
  leases: number
  work_orders_open: number
  ar_outstanding: number
}

async function getMe() {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
  const res = await fetch(`${baseUrl}/api/me`, { cache: "no-store" })
  if (res.ok) return res.json()
  return null
}

async function getSummary() {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
  const res = await fetch(`${baseUrl}/api/summary`, { cache: "no-store" })
  if (res.ok) return res.json()
  return null
}

export default async function DashboardPage() {
  const me = await getMe()
  if (!me) {
    // Not signed in
    return (
      <div className="p-6">
        <div className="rounded-md border border-border bg-card text-card-foreground p-6">
          <h1 className="text-lg font-semibold">Session expired</h1>
          <p className="text-sm text-foreground/70 mt-1">Please sign in again.</p>
          <Link href="/login" className="mt-4 inline-flex rounded-md bg-primary px-4 py-2 text-primary-foreground">
            Go to Login
          </Link>
        </div>
      </div>
    )
  }

  const summary: Summary | null = await getSummary()

  const modules = [
    { key: "property", label: "Property", href: "/dashboard/property", roles: ["admin", "manager"] },
    { key: "tenant", label: "Tenant", href: "/dashboard/tenant", roles: ["admin", "manager"] },
    { key: "lease", label: "Lease", href: "/dashboard/lease", roles: ["admin", "manager", "finance"] },
    {
      key: "maintenance",
      label: "Maintenance",
      href: "/dashboard/maintenance",
      roles: ["admin", "manager", "fm", "staff"],
    },
    { key: "finance", label: "Finance", href: "/dashboard/finance", roles: ["admin", "finance"] },
    { key: "legal", label: "Legal", href: "/dashboard/legal", roles: ["admin", "manager", "legal"] },
    { key: "crm", label: "CRM", href: "/dashboard/crm", roles: ["admin", "manager", "frontdesk"] },
    { key: "staff", label: "Staff", href: "/dashboard/staff", roles: ["admin", "manager"] },
  ]

  const userRoles: string[] = me?.roles || []

  const visibleModules = modules.filter((m) => m.roles.some((r) => userRoles.includes(r)))

  return (
    <div className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Welcome, {me?.full_name || "User"}</h1>
          <p className="text-sm text-foreground/70">
            Org: {me?.org?.name} • Roles: {userRoles.join(", ") || "—"}
          </p>
        </div>
      </div>

      <section className="mt-6 grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard title="Properties" value={summary?.properties ?? 0} />
        <KpiCard title="Spaces" value={summary?.spaces ?? 0} />
        <KpiCard title="Leases" value={summary?.leases ?? 0} />
        <KpiCard title="Open WOs" value={summary?.work_orders_open ?? 0} />
      </section>

      <section className="mt-8">
        <h2 className="text-lg font-semibold">Modules</h2>
        <div className="mt-3 grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
          {visibleModules.map((m) => (
            <Link
              key={m.key}
              href={m.href}
              className="rounded-md border border-border bg-card text-card-foreground p-4 hover:bg-muted"
            >
              <div className="font-medium">{m.label}</div>
              <div className="text-sm text-foreground/70">Access: {m.roles.join(", ")}</div>
            </Link>
          ))}
        </div>
      </section>
    </div>
  )
}

function KpiCard({ title, value }: { title: string; value: number | string }) {
  return (
    <div className="rounded-md border border-border bg-card text-card-foreground p-4">
      <div className="text-sm text-foreground/70">{title}</div>
      <div className="mt-2 text-2xl font-semibold">{value}</div>
    </div>
  )
}
