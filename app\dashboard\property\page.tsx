export default async function PropertyModulePage() {
  const res = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || ""}/api/properties`, { cache: "no-store" })
  const data = res.ok ? await res.json() : []
  return (
    <div className="p-6">
      <h1 className="text-xl font-semibold">Property / Spaces</h1>
      <div className="mt-4 overflow-x-auto rounded-md border border-border">
        <table className="min-w-full text-sm">
          <thead className="bg-muted">
            <tr>
              <th className="px-3 py-2 text-left">Code</th>
              <th className="px-3 py-2 text-left">Name</th>
              <th className="px-3 py-2 text-left">Kind</th>
              <th className="px-3 py-2 text-left">Status</th>
            </tr>
          </thead>
          <tbody>
            {data.map((s: any) => (
              <tr key={s.id} className="border-t border-border">
                <td className="px-3 py-2">{s.code}</td>
                <td className="px-3 py-2">{s.name}</td>
                <td className="px-3 py-2">{s.kind}</td>
                <td className="px-3 py-2">{s.status}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
