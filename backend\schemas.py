from pydantic import BaseModel
from typing import List, Optional

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int = 3600

class MeOrg(BaseModel):
    id: str
    name: str

class MeUser(BaseModel):
    id: str
    full_name: str
    email: str
    roles: List[str]

class ModuleItem(BaseModel):
    key: str
    label: str
    path: str

class MeResponse(BaseModel):
    org: MeOrg
    user: MeUser
    modules: List[ModuleItem]

class SummaryResponse(BaseModel):
    occupancy: float
    open_work_orders: int
    rent_due: float
    compliance_alerts: int

class SpaceOut(BaseModel):
    id: str
    code: str
    kind: str
    status: str
    area_sqft: Optional[float] = None

class LeaseOut(BaseModel):
    id: str
    space_code: str
    start_date: str
    end_date: str
    rent_amount: float
    status: str

class ServiceRequestOut(BaseModel):
    id: str
    category: str
    priority: str
    status: str
    created_at: str
    space_code: Optional[str] = None
