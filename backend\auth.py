import os, hashlib, hmac, time, base64, json
from typing import List

JWT_SECRET = os.getenv("JWT_SECRET", "dev-secret-change-me")
JWT_ISSUER = "facilityos"
JWT_EXPIRES_IN = 3600

def hash_password(password: str) -> str:
    return hashlib.sha256(password.encode("utf-8")).hexdigest()

def verify_password(password: str, password_hash: str) -> bool:
    return hash_password(password) == password_hash

def _base64url(data: bytes) -> str:
    return base64.urlsafe_b64encode(data).rstrip(b"=").decode("ascii")

def sign_jwt(sub: str, org_id: str, roles: List[str]) -> str:
    header = {"alg":"HS256","typ":"JWT"}
    now = int(time.time())
    payload = {
        "iss": JWT_ISSUER,
        "sub": sub,
        "org_id": org_id,
        "roles": roles,
        "iat": now,
        "exp": now + JWT_EXPIRES_IN
    }
    header_b64 = _base64url(json.dumps(header, separators=(",",":")).encode())
    payload_b64 = _base64url(json.dumps(payload, separators=(",",":")).encode())
    to_sign = f"{header_b64}.{payload_b64}".encode()
    signature = hmac.new(JWT_SECRET.encode(), to_sign, hashlib.sha256).digest()
    sig_b64 = _base64url(signature)
    return f"{header_b64}.{payload_b64}.{sig_b64}"

def decode_jwt(token: str) -> dict:
    try:
        header_b64, payload_b64, sig_b64 = token.split(".")
        to_sign = f"{header_b64}.{payload_b64}".encode()
        expected = hmac.new(JWT_SECRET.encode(), to_sign, hashlib.sha256).digest()
        if not hmac.compare_digest(base64.urlsafe_b64decode(sig_b64 + "=="), expected):
            raise ValueError("invalid signature")
        payload = json.loads(base64.urlsafe_b64decode(payload_b64 + "=="))
        if payload.get("exp", 0) < int(time.time()):
            raise ValueError("expired")
        return payload
    except Exception:
        raise
