export default async function MaintenanceModulePage() {
  const res = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || ""}/api/work-orders`, { cache: "no-store" })
  const data = res.ok ? await res.json() : []
  return (
    <div className="p-6">
      <h1 className="text-xl font-semibold">Service Requests</h1>
      <div className="mt-4 overflow-x-auto rounded-md border border-border">
        <table className="min-w-full text-sm">
          <thead className="bg-muted">
            <tr>
              <th className="px-3 py-2 text-left">Category</th>
              <th className="px-3 py-2 text-left">Priority</th>
              <th className="px-3 py-2 text-left">Status</th>
              <th className="px-3 py-2 text-left">Created</th>
              <th className="px-3 py-2 text-left">Space</th>
            </tr>
          </thead>
          <tbody>
            {data.map((r: any) => (
              <tr key={r.id} className="border-t border-border">
                <td className="px-3 py-2">{r.category}</td>
                <td className="px-3 py-2">{r.priority}</td>
                <td className="px-3 py-2">{r.status}</td>
                <td className="px-3 py-2">{r.created_at}</td>
                <td className="px-3 py-2">{r.space_code || "-"}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
