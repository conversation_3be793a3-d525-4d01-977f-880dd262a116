export function getFastApiBase(): string {
  // Read env and normalize
  const raw = process.env.FASTAPI_BASE_URL ?? ""
  const trimmed = raw.trim()
  // Fallback for local development if unset
  const base = trimmed || "http://localhost:8000"
  // Remove trailing slashes for consistent URL building
  return base.replace(/\/+$/, "")
}

export function isSecureCookie(): boolean {
  // If NEXT_PUBLIC_APP_URL is https, set secure=true; otherwise false for local http
  const appUrl = (process.env.NEXT_PUBLIC_APP_URL || "").trim()
  if (appUrl.startsWith("https://")) return true
  if (appUrl.startsWith("http://")) return false
  // Default to true in hosted previews
  return true
}
