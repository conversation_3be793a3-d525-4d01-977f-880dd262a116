import { NextResponse } from "next/server"

function normalize(url?: string | null) {
  if (!url) return null
  const trimmed = url.trim()
  if (!trimmed) return null
  return trimmed.endsWith("/") ? trimmed.slice(0, -1) : trimmed
}

export async function GET() {
  const raw = process.env.FASTAPI_BASE_URL ?? null
  const base = normalize(raw)
  if (!base) {
    return NextResponse.json({ ok: false, error: "FASTAPI_BASE_URL not set on server." }, { status: 500 })
  }

  // Try /health first, then /docs as a fallback visibility check
  const targets = [`${base}/health`, `${base}/docs`]
  const results: Array<{ url: string; ok: boolean; status?: number; error?: string }> = []

  for (const url of targets) {
    try {
      const r = await fetch(url, { method: "GET", headers: { accept: "application/json, text/html" } })
      results.push({ url, ok: r.ok, status: r.status })
      if (r.ok) break
    } catch (e: any) {
      results.push({ url, ok: false, error: e?.message || "fetch failed" })
    }
  }

  const overallOk = results.some((r) => r.ok)
  return NextResponse.json({ ok: overallOk, attempts: results })
}
