async function getLeases() {
  const res = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || ""}/api/leases?limit=20`, { cache: "no-store" })
  if (!res.ok) return []
  return res.json() as Promise<
    Array<{ id: string; space_code: string; start_date: string; end_date: string; rent_amount: number; status: string }>
  >
}
export default async function LeaseModulePage() {
  const leases = await getLeases()
  return (
    <div>
      <h1 className="text-2xl font-semibold">Lease & Agreements</h1>
      <div className="mt-4 rounded-lg border border-border bg-card text-card-foreground">
        <table className="w-full text-sm">
          <thead className="bg-muted text-left text-foreground/70">
            <tr>
              <th className="px-3 py-2">Lease</th>
              <th className="px-3 py-2">Space</th>
              <th className="px-3 py-2">Start</th>
              <th className="px-3 py-2">End</th>
              <th className="px-3 py-2">Rent</th>
              <th className="px-3 py-2">Status</th>
            </tr>
          </thead>
          <tbody>
            {leases.map((l) => (
              <tr key={l.id} className="border-t border-border">
                <td className="px-3 py-2">{l.id.slice(0, 8)}</td>
                <td className="px-3 py-2">{l.space_code}</td>
                <td className="px-3 py-2">{l.start_date}</td>
                <td className="px-3 py-2">{l.end_date}</td>
                <td className="px-3 py-2">₹{l.rent_amount.toLocaleString()}</td>
                <td className="px-3 py-2">{l.status}</td>
              </tr>
            ))}
            {leases.length === 0 && (
              <tr>
                <td className="px-3 py-6 text-foreground/60" colSpan={6}>
                  No leases found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}
