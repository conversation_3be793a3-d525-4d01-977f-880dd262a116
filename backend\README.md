# FacilityOS FastAPI Backend

- Tech: FastAPI, SQLAlchemy, SQLite (mock data)
- Auth: Email+password (demo) → JWT (HttpOnly cookie set by Next.js route)
- Multi-tenant: org_id carried in JWT and enforced by list endpoints

Run locally:
1) Create venv and install deps: fastapi, uvicorn, sqlalchemy, pydantic
2) Seed DB: `python -m backend.seed`
3) Start server: `uvicorn backend.main:app --reload --port 8001`

Environment:
- JWT_SECRET: set a strong secret in production

API:
- POST /auth/login { email, password } → { access_token }
- GET /me → user + allowed modules
- GET /summary → KPIs
- GET /spaces, /leases, /service-requests → lists
