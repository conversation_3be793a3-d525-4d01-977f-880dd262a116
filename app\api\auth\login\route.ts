import { type NextRequest, NextResponse } from "next/server"
import { getFastApiBase, isSecureCookie } from "@/app/lib/env"

export async function POST(req: NextRequest) {
  const { email, password } = await req.json()
  const base = getFastApiBase()
  if (!base) return new NextResponse("FASTAPI_BASE_URL not set", { status: 500 })

  const res = await fetch(`${base}/auth/login`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ email, password }),
  })
  if (!res.ok) {
    const text = await res.text()
    return new NextResponse(text || "Invalid credentials", { status: 401 })
  }
  const data = (await res.json()) as { access_token: string; token_type: string; expires_in: number }
  const resp = new NextResponse(null, { status: 204 })
  const secure = isSecureCookie()
  resp.cookies.set({
    name: "fm_jwt",
    value: data.access_token,
    httpOnly: true,
    sameSite: "lax",
    secure,
    path: "/",
    maxAge: data.expires_in || 60 * 60,
  })
  return resp
}
