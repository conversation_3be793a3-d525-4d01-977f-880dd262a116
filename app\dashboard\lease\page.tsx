export default async function LeaseModulePage() {
  const res = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || ""}/api/leases`, { cache: "no-store" })
  const data = res.ok ? await res.json() : []
  return (
    <div className="p-6">
      <h1 className="text-xl font-semibold">Leases</h1>
      <div className="mt-4 overflow-x-auto rounded-md border border-border">
        <table className="min-w-full text-sm">
          <thead className="bg-muted">
            <tr>
              <th className="px-3 py-2 text-left">Space</th>
              <th className="px-3 py-2 text-left">Start</th>
              <th className="px-3 py-2 text-left">End</th>
              <th className="px-3 py-2 text-left">Rent</th>
              <th className="px-3 py-2 text-left">Status</th>
            </tr>
          </thead>
          <tbody>
            {data.map((l: any) => (
              <tr key={l.id} className="border-t border-border">
                <td className="px-3 py-2">{l.space_code}</td>
                <td className="px-3 py-2">{l.start_date}</td>
                <td className="px-3 py-2">{l.end_date}</td>
                <td className="px-3 py-2">{l.rent_amount}</td>
                <td className="px-3 py-2">{l.status}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
