from sqlalchemy import Column, String, DateTime, ForeignKey, Integer, Numeric, Text
from sqlalchemy.dialects.sqlite import BLOB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.types import JSO<PERSON>
from .database import Base
import uuid

def uuid_str():
    return str(uuid.uuid4())

class Org(Base):
    __tablename__ = "orgs"
    id = Column(String, primary_key=True, default=uuid_str)
    name = Column(String, nullable=False)
    status = Column(String, default="active")
    created_at = Column(DateTime, server_default=func.now())

class Site(Base):
    __tablename__ = "sites"
    id = Column(String, primary_key=True, default=uuid_str)
    org_id = Column(String, ForeignKey("orgs.id"), nullable=False)
    name = Column(String, nullable=False)
    code = Column(String)
    kind = Column(String, nullable=False)  # residential|commercial|hotel|mall|mixed|campus
    status = Column(String, default="active")
    created_at = Column(DateTime, server_default=func.now())
    org = relationship("Org")

class User(Base):
    __tablename__ = "users"
    id = Column(String, primary_key=True, default=uuid_str)
    org_id = Column(String, ForeignKey("orgs.id"), nullable=False)
    full_name = Column(String, nullable=False)
    email = Column(String, nullable=False)
    password_hash = Column(String, nullable=False)
    status = Column(String, default="active")
    created_at = Column(DateTime, server_default=func.now())
    org = relationship("Org")

class Role(Base):
    __tablename__ = "roles"
    id = Column(String, primary_key=True, default=uuid_str)
    org_id = Column(String, ForeignKey("orgs.id"), nullable=False)
    name = Column(String, nullable=False)  # super_admin, manager, tenant, staff, finance
    description = Column(Text)

class UserRole(Base):
    __tablename__ = "user_roles"
    user_id = Column(String, ForeignKey("users.id"), primary_key=True)
    role_id = Column(String, ForeignKey("roles.id"), primary_key=True)

class Space(Base):
    __tablename__ = "spaces"
    id = Column(String, primary_key=True, default=uuid_str)
    org_id = Column(String, ForeignKey("orgs.id"), nullable=False)
    site_id = Column(String, ForeignKey("sites.id"), nullable=False)
    code = Column(String, nullable=False)
    kind = Column(String, nullable=False) # apartment|shop|room|office
    status = Column(String, default="available")
    area_sqft = Column(Numeric(12,2))

class Lease(Base):
    __tablename__ = "leases"
    id = Column(String, primary_key=True, default=uuid_str)
    org_id = Column(String, ForeignKey("orgs.id"), nullable=False)
    site_id = Column(String, ForeignKey("sites.id"), nullable=False)
    space_id = Column(String, ForeignKey("spaces.id"))
    start_date = Column(String, nullable=False)
    end_date = Column(String, nullable=False)
    rent_amount = Column(Numeric(14,2), nullable=False)
    status = Column(String, default="active")

class ServiceRequest(Base):
    __tablename__ = "service_requests"
    id = Column(String, primary_key=True, default=uuid_str)
    org_id = Column(String, ForeignKey("orgs.id"), nullable=False)
    site_id = Column(String, ForeignKey("sites.id"), nullable=False)
    space_id = Column(String, ForeignKey("spaces.id"))
    category = Column(String)
    priority = Column(String, default="medium")
    status = Column(String, default="open")
    description = Column(Text)
    created_at = Column(DateTime, server_default=func.now())
