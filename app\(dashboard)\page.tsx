async function getSummary() {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
  const res = await fetch(`${baseUrl}/api/summary`, { cache: "no-store" })
  if (!res.ok) return null
  return res.json() as Promise<{
    occupancy: number
    open_work_orders: number
    rent_due: number
    compliance_alerts: number
  }>
}

export default async function DashboardPage() {
  const summary = await getSummary()
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-semibold">Dashboard</h1>
      <div className="grid gap-4 md:grid-cols-4">
        {[
          { label: "Occupancy", value: summary?.occupancy ?? 0, suffix: "%" },
          { label: "Open Work Orders", value: summary?.open_work_orders ?? 0 },
          { label: "Rent Due (₹)", value: summary?.rent_due ?? 0 },
          { label: "Compliance Alerts", value: summary?.compliance_alerts ?? 0 },
        ].map((kpi) => (
          <div key={kpi.label} className="rounded-lg border border-border bg-card p-4 text-card-foreground">
            <div className="text-sm text-foreground/60">{kpi.label}</div>
            <div className="mt-1 text-2xl font-semibold">
              {kpi.value}
              {kpi.suffix || ""}
            </div>
          </div>
        ))}
      </div>
      <div className="grid gap-6 md:grid-cols-2">
        <div className="rounded-lg border border-border bg-card p-4 text-card-foreground">
          <div className="text-sm font-medium">Recent Service Requests</div>
          <div className="mt-3 h-32 rounded border border-dashed text-sm text-foreground/60 grid place-items-center">
            Link: Modules → Maintenance
          </div>
        </div>
        <div className="rounded-lg border border-border bg-card p-4 text-card-foreground">
          <div className="text-sm font-medium">Lease Expiries</div>
          <div className="mt-3 h-32 rounded border border-dashed text-sm text-foreground/60 grid place-items-center">
            Link: Modules → Lease & Agreements
          </div>
        </div>
      </div>
    </div>
  )
}
