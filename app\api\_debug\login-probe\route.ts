import { NextResponse } from "next/server"

function normalize(url?: string | null) {
  if (!url) return null
  const trimmed = url.trim()
  if (!trimmed) return null
  return trimmed.endsWith("/") ? trimmed.slice(0, -1) : trimmed
}

export async function POST(req: Request) {
  const raw = process.env.FASTAPI_BASE_URL ?? null
  const base = normalize(raw)
  if (!base) {
    return NextResponse.json({ ok: false, error: "FASTAPI_BASE_URL not set on server." }, { status: 500 })
  }

  const body = await req.json().catch(() => ({}))
  const email = body?.email || body?.username
  const password = body?.password

  if (!email || !password) {
    return NextResponse.json({ ok: false, error: "Missing email/username or password" }, { status: 400 })
  }

  // Strategy: try JSON first, then fallback to form-encoded (OAuth2PasswordRequestForm style)
  const targets: Array<{ url: string; init: RequestInit; label: string }> = []

  // Attempt 1: JSON payload expected by custom /auth/login
  targets.push({
    label: "json",
    url: `${base}/auth/login`,
    init: {
      method: "POST",
      headers: { "content-type": "application/json" },
      body: JSON.stringify({ email, password }),
    },
  })

  // Attempt 2: x-www-form-urlencoded payload with username/password (OAuth2)
  const form = new URLSearchParams()
  form.set("username", email)
  form.set("password", password)
  targets.push({
    label: "form",
    url: `${base}/auth/login`,
    init: {
      method: "POST",
      headers: { "content-type": "application/x-www-form-urlencoded" },
      body: form.toString(),
    },
  })

  const attempts: any[] = []
  for (const t of targets) {
    try {
      const r = await fetch(t.url, t.init)
      const text = await r.text()
      attempts.push({ label: t.label, url: t.url, status: r.status, ok: r.ok, body: text })
      if (r.ok) {
        // Return raw backend response for inspection
        return new NextResponse(text, {
          status: r.status,
          headers: { "content-type": r.headers.get("content-type") || "application/json" },
        })
      }
    } catch (e: any) {
      attempts.push({ label: t.label, url: t.url, ok: false, error: e?.message || "fetch failed" })
    }
  }

  return NextResponse.json({ ok: false, error: "Backend login failed", attempts }, { status: 502 })
}
