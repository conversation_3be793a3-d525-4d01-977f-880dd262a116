"""
Start the FastAPI backend locally.

Behavior:
- Imports backend.main:app
- Runs uvicorn on host 0.0.0.0 and port (PORT env or 8000 by default)
- Prints the effective base URL so you can copy it into FASTAPI_BASE_URL
- For CORS, ensure backend/main.py includes CORSMiddleware allowing NEXT_PUBLIC_APP_URL

Run from v0 Scripts panel: "Start API"
"""

import os
import sys
import time

def main() -> int:
    port = int(os.environ.get("PORT", "8000"))
    host = os.environ.get("HOST", "0.0.0.0")
    try:
        from uvicorn import run as uvicorn_run  # type: ignore
    except Exception as e:
        print(f"[start] uvicorn not available: {e}")
        print("[start] Please ensure uvicorn is available or run: uvicorn backend.main:app --host 0.0.0.0 --port 8000")
        return 1

    try:
        from backend.main import app  # type: ignore
    except Exception as e:
        print(f"[start] Failed to import backend.main: {e}")
        print("[start] Ensure backend/main.py exists with a FastAPI `app` instance.")
        return 1

    base_url = f"http://{host}:{port}"
    print(f"[start] Launching FastAPI on {base_url}")
    print("[start] Copy http://localhost:8000 (or your host/port) into FASTAPI_BASE_URL after starting.")
    next_public_app = os.environ.get("NEXT_PUBLIC_APP_URL", "")
    if next_public_app:
        print(f"[start] Frontend origin: {next_public_app} (ensure CORS allows this origin)")

    # Run server (blocking)
    uvicorn_run(app=app, host=host, port=port, reload=False)
    return 0

if __name__ == "__main__":
    sys.exit(main())
