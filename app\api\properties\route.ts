import { cookies } from "next/headers"
import { NextResponse } from "next/server"

export async function GET() {
  const token = cookies().get("fm_jwt")?.value
  const base = process.env.FASTAPI_BASE_URL || "http://localhost:8000"
  const res = await fetch(`${base}/spaces`, {
    headers: token ? { Authorization: `Bearer ${token}` } : {},
    cache: "no-store",
  })
  const data = await res.json().catch(() => [])
  return NextResponse.json(data, { status: res.status })
}
