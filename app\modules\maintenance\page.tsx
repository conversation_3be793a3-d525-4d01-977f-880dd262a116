async function getServiceRequests() {
  const res = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || ""}/api/work-orders?limit=20`, { cache: "no-store" })
  if (!res.ok) return []
  return res.json() as Promise<
    Array<{ id: string; category: string; status: string; priority: string; created_at: string; space_code?: string }>
  >
}

export default async function MaintenanceModulePage() {
  const data = await getServiceRequests()
  return (
    <div>
      <h1 className="text-2xl font-semibold">Maintenance & Service Requests</h1>
      <div className="mt-4 rounded-lg border border-border bg-card text-card-foreground">
        <table className="w-full text-sm">
          <thead className="bg-muted text-left text-foreground/70">
            <tr>
              <th className="px-3 py-2">ID</th>
              <th className="px-3 py-2">Category</th>
              <th className="px-3 py-2">Priority</th>
              <th className="px-3 py-2">Status</th>
              <th className="px-3 py-2">Space</th>
              <th className="px-3 py-2">Created</th>
            </tr>
          </thead>
          <tbody>
            {data.map((r) => (
              <tr key={r.id} className="border-t border-border">
                <td className="px-3 py-2">{r.id.slice(0, 8)}</td>
                <td className="px-3 py-2">{r.category}</td>
                <td className="px-3 py-2">{r.priority}</td>
                <td className="px-3 py-2">{r.status}</td>
                <td className="px-3 py-2">{r.space_code ?? "-"}</td>
                <td className="px-3 py-2">{new Date(r.created_at).toLocaleString()}</td>
              </tr>
            ))}
            {data.length === 0 && (
              <tr>
                <td className="px-3 py-6 text-foreground/60" colSpan={6}>
                  No service requests found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}
