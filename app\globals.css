@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: #ffffff;
  --foreground: #3c3c3c;

  --card: #f7f7f7;
  --card-foreground: #3c3c3c;

  --popover: #ffffff;
  --popover-foreground: #3c3c3c;

  --primary: #003366;
  --primary-foreground: #ffffff;

  --secondary: #66b3a1;
  --secondary-foreground: #003366;

  --muted: #f7f7f7;
  --muted-foreground: #3c3c3c;

  --accent: #66b3a1;
  --accent-foreground: #ffffff;

  --destructive: #ff4d4d;
  --destructive-foreground: #ffffff;

  --border: #e0e0e0;
  --input: #ffffff;
  --ring: #66b3a1;

  /* Charts aligned to palette */
  --chart-1: #003366;
  --chart-2: #66b3a1;
  --chart-3: #f7f7f7;
  --chart-4: #3c3c3c;
  --chart-5: #ffffff;

  /* Radius per brief */
  --radius: 0.25rem;

  /* Sidebar */
  --sidebar: #f7f7f7;
  --sidebar-foreground: #3c3c3c;
  --sidebar-primary: #003366;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #66b3a1;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #e0e0e0;
  --sidebar-ring: #66b3a1;
}

.dark {
  /* Dark background with clear text */
  --background: #3c3c3c;
  --foreground: #ffffff;

  /* Cards/popovers use brand blue for separation and identity */
  --card: #003366;
  --card-foreground: #ffffff;

  --popover: #003366;
  --popover-foreground: #ffffff;

  /* Keep brand color available; accent remains for emphasis */
  --primary: #003366;
  --primary-foreground: #ffffff;

  --secondary: #f7f7f7;
  --secondary-foreground: #003366;

  /* Muted surfaces blend into dark background */
  --muted: #3c3c3c;
  --muted-foreground: #f7f7f7;

  --accent: #66b3a1;
  --accent-foreground: #ffffff;

  --destructive: #ff4d4d;
  --destructive-foreground: #ffffff;

  /* Borders and focus stay within palette */
  --border: #66b3a1;
  --input: #3c3c3c;
  --ring: #66b3a1;

  /* Charts */
  --chart-1: #66b3a1;
  --chart-2: #003366;
  --chart-3: #f7f7f7;
  --chart-4: #ffffff;
  --chart-5: #3c3c3c;

  /* Sidebar */
  --sidebar: #003366;
  --sidebar-foreground: #ffffff;
  --sidebar-primary: #66b3a1;
  --sidebar-primary-foreground: #003366;
  --sidebar-accent: #66b3a1;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #66b3a1;
  --sidebar-ring: #66b3a1;
}

@theme inline {
  --font-sans: var(--font-dm-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
