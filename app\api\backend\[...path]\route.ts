import { type NextRequest, NextResponse } from "next/server"
import { getFastApiBase } from "@/app/lib/env"

export async function GET(req: NextRequest, { params }: { params: { path: string[] } }) {
  const token = req.cookies.get("fm_jwt")?.value
  const base = getFastApiBase()
  if (!token) return new NextResponse("Unauthorized", { status: 401 })

  const url = new URL(req.url)
  const qs = url.search
  const resource = params.path.join("/")
  const target = `${base}/${resource}${qs}`
  const res = await fetch(target, {
    headers: { Authorization: `Bearer ${token}` },
    cache: "no-store",
  })
  const text = await res.text()
  return new NextResponse(text, {
    status: res.status,
    headers: { "Content-Type": res.headers.get("Content-Type") || "application/json" },
  })
}
