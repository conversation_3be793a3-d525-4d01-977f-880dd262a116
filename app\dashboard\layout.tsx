import type React from "react"
import Link from "next/link"

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen grid grid-cols-1 md:grid-cols-[260px_1fr]">
      <aside className="border-r border-sidebar-border bg-sidebar text-sidebar-foreground">
        <div className="flex items-center gap-2 px-4 py-4 border-b border-sidebar-border">
          <div className="h-8 w-8 rounded bg-sidebar-primary" aria-hidden />
          <span className="font-semibold">FacilityOS</span>
        </div>
        <nav className="p-3 grid gap-1">
          {[
            { href: "/dashboard", label: "Overview" },
            { href: "/dashboard/property", label: "Property" },
            { href: "/dashboard/tenant", label: "Tenant" },
            { href: "/dashboard/lease", label: "Lease" },
            { href: "/dashboard/maintenance", label: "Maintenance" },
            { href: "/dashboard/finance", label: "Finance" },
            { href: "/dashboard/legal", label: "Legal" },
            { href: "/dashboard/crm", label: "CRM" },
            { href: "/dashboard/staff", label: "Staff" },
          ].map((i) => (
            <Link
              key={i.href}
              href={i.href}
              className="rounded-md px-3 py-2 text-sm hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
            >
              {i.label}
            </Link>
          ))}
          <form action="/api/auth/logout" method="post" className="mt-4">
            <button className="w-full rounded-md bg-destructive/10 text-destructive border border-destructive px-3 py-2 text-sm hover:bg-destructive hover:text-destructive-foreground">
              Logout
            </button>
          </form>
        </nav>
      </aside>
      <main className="bg-background">{children}</main>
    </div>
  )
}
