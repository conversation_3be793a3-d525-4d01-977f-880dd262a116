import { NextResponse } from "next/server"

function normalize(url?: string | null) {
  if (!url) return null
  const trimmed = url.trim()
  if (!trimmed) return null
  return trimmed.endsWith("/") ? trimmed.slice(0, -1) : trimmed
}

export async function GET() {
  const rawFastapi = process.env.FASTAPI_BASE_URL ?? null
  const rawAppUrl = process.env.NEXT_PUBLIC_APP_URL ?? null
  const fastapi = normalize(rawFastapi)
  const appUrl = normalize(rawAppUrl)
  const usingFallback = !fastapi

  return NextResponse.json({
    FASTAPI_BASE_URL: rawFastapi ?? null,
    NEXT_PUBLIC_APP_URL: rawAppUrl ?? null,
    resolved: {
      fastapi,
      appUrl,
    },
    notes: usingFallback
      ? "FASTAPI_BASE_URL is not resolved on the server. Check Project Settings > Environment Variables."
      : "FASTAPI_BASE_URL resolved on the server.",
  })
}
