import Link from "next/link"

export default function HomePage() {
  return (
    <main className="min-h-screen bg-background text-foreground">
      <header className="border-b border-border">
        <div className="mx-auto max-w-6xl px-6 py-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="h-8 w-8 rounded bg-primary" aria-hidden />
            <span className="font-semibold">FacilityOS</span>
          </div>
          <nav className="flex items-center gap-6 text-sm">
            <a href="#features" className="text-foreground/70 hover:text-foreground">
              Features
            </a>
            <a href="#security" className="text-foreground/70 hover:text-foreground">
              Security
            </a>
            <a href="#pricing" className="text-foreground/70 hover:text-foreground">
              Pricing
            </a>
            <Link
              href="/login"
              className="inline-flex items-center rounded-md bg-primary px-4 py-2 text-primary-foreground hover:opacity-90"
            >
              Login
            </Link>
          </nav>
        </div>
      </header>

      <section className="mx-auto max-w-6xl px-6 py-20">
        <div className="grid gap-8 md:grid-cols-2 md:items-center">
          <div>
            <h1 className="text-pretty text-4xl font-semibold">Enterprise Facility & Property Management, unified.</h1>
            <p className="mt-4 leading-relaxed text-foreground/80">
              Manage assets, leases, tenants, work orders, invoices, and compliance across multi-org and multi-site
              operations—securely and at scale.
            </p>
            <div className="mt-8 flex gap-3">
              <Link
                href="/login"
                className="inline-flex items-center rounded-md bg-primary px-5 py-2.5 text-primary-foreground hover:opacity-90"
              >
                Go to Login
              </Link>
              <a
                href="#features"
                className="inline-flex items-center rounded-md border border-border px-5 py-2.5 hover:bg-muted"
              >
                Explore Features
              </a>
            </div>
            <div className="mt-6 text-xs text-foreground/60">
              Palette: primary blue, neutrals, accent green. WCAG AA compliant.
            </div>
          </div>
          <div>
            <img
              src="/facility-management-dashboard-preview.png"
              alt="Preview of the FacilityOS dashboard UI"
              className="rounded-lg border border-border"
            />
          </div>
        </div>

        <div id="features" className="mt-16 grid gap-6 md:grid-cols-3">
          {[
            { title: "RBAC & Multi-Tenant", desc: "Org- and site-scoped access with roles and policies." },
            { title: "Operational Modules", desc: "Property, Lease, Maintenance, Finance, CRM, Legal, Staff." },
            { title: "Dashboards & KPIs", desc: "Role-based insights: occupancy, SLAs, AR, compliance." },
          ].map((f) => (
            <div key={f.title} className="rounded-lg border border-border p-5 bg-card text-card-foreground">
              <h3 className="font-medium">{f.title}</h3>
              <p className="mt-1 text-sm text-foreground/80">{f.desc}</p>
            </div>
          ))}
        </div>

        <div id="security" className="mt-16 rounded-lg bg-muted p-6 border border-border">
          <h2 className="text-xl font-semibold">Security by design</h2>
          <ul className="mt-3 list-disc pl-5 text-sm leading-relaxed">
            <li>HttpOnly JWT session</li>
            <li>Least-privilege roles and module scoping</li>
            <li>Audit-friendly API with org_id enforcement</li>
          </ul>
        </div>
      </section>

      <footer className="border-t border-border">
        <div className="mx-auto max-w-6xl px-6 py-8 text-sm text-foreground/60">
          © {new Date().getFullYear()} FacilityOS. All rights reserved.
        </div>
      </footer>
    </main>
  )
}
